"""
Exports function list_versions() for unicode version level support.

This code generated by wcwidth/bin/update-tables.py on 2023-09-14 15:45:33 UTC.
"""


def list_versions():
    """
    Return Unicode version levels supported by this module release.

    Any of the version strings returned may be used as keyword argument
    ``unicode_version`` to the ``wcwidth()`` family of functions.

    :returns: Supported Unicode version numbers in ascending sorted order.
    :rtype: list[str]
    """
    return (
        "4.1.0",
        "5.0.0",
        "5.1.0",
        "5.2.0",
        "6.0.0",
        "6.1.0",
        "6.2.0",
        "6.3.0",
        "7.0.0",
        "8.0.0",
        "9.0.0",
        "10.0.0",
        "11.0.0",
        "12.0.0",
        "12.1.0",
        "13.0.0",
        "14.0.0",
        "15.0.0",
        "15.1.0",
    )
